import React from 'react';
import { useThemeStore } from '../../stores/themeStore';
import type { LoginMode } from './LoginScreen';

export interface AuthTab {
  id: LoginMode;
  label: string;
  icon: React.ReactNode;
  description?: string;
}

export interface AuthNavigationTabsProps {
  currentMode: LoginMode;
  onModeChange: (mode: LoginMode) => void;
  tabs?: AuthTab[];
  disabled?: boolean;
  className?: string;
  'data-testid'?: string;
}

const defaultTabs: AuthTab[] = [
  {
    id: 'login',
    label: 'Sign In',
    description: 'Email & Password',
    icon: (
      <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
      </svg>
    ),
  },
  {
    id: 'otp',
    label: 'OTP Login',
    description: 'Phone Verification',
    icon: (
      <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 18h.01M8 21h8a2 2 0 002-2V5a2 2 0 00-2-2H8a2 2 0 00-2 2v14a2 2 0 002 2z" />
      </svg>
    ),
  },
  {
    id: 'forgot',
    label: 'Reset',
    description: 'Forgot Password',
    icon: (
      <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 7a2 2 0 012 2m4 0a6 6 0 01-7.743 5.743L11 17H9v2H7v2H4a1 1 0 01-1-1v-2.586a1 1 0 01.293-.707l5.964-5.964A6 6 0 1121 9z" />
      </svg>
    ),
  },
  {
    id: 'access-request',
    label: 'Request',
    description: 'Access Request',
    icon: (
      <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
      </svg>
    ),
  },
];

export function AuthNavigationTabs({
  currentMode,
  onModeChange,
  tabs = defaultTabs,
  disabled = false,
  className = '',
  'data-testid': testId,
}: AuthNavigationTabsProps) {
  const { colors } = useThemeStore();

  return (
    <div
      className={`sticky bottom-0 left-0 right-0 z-10 ${className}`}
      data-testid={testId}
    >
      {/* Background with blur effect */}
      <div className="absolute inset-0 bg-white/80 dark:bg-slate-900/80 backdrop-blur-md border-t border-slate-200 dark:border-slate-700" />
      
      {/* Navigation content */}
      <div className="relative px-4 py-3">
        <div className="flex items-center justify-center">
          <div className="flex bg-slate-100 dark:bg-slate-800 rounded-xl p-1 gap-1">
            {tabs.map((tab) => {
              const isActive = tab.id === currentMode;
              
              return (
                <button
                  key={tab.id}
                  onClick={() => !disabled && onModeChange(tab.id)}
                  disabled={disabled}
                  className={`
                    relative flex flex-col items-center justify-center px-3 py-2 rounded-lg
                    transition-all duration-200 ease-out min-w-[80px] group
                    ${isActive 
                      ? 'text-white shadow-lg transform scale-105' 
                      : 'text-slate-600 dark:text-slate-400 hover:text-slate-900 dark:hover:text-slate-200'
                    }
                    ${disabled ? 'opacity-50 cursor-not-allowed' : 'cursor-pointer'}
                    ${!isActive && !disabled ? 'hover:bg-slate-200 dark:hover:bg-slate-700' : ''}
                  `}
                  style={isActive ? {
                    backgroundColor: colors.primary,
                    boxShadow: `0 4px 12px ${colors.primary}40`,
                  } : {}}
                  data-testid={`auth-tab-${tab.id}`}
                >
                  {/* Icon */}
                  <div className={`mb-1 transition-transform duration-200 ${isActive ? 'scale-110' : 'group-hover:scale-105'}`}>
                    {tab.icon}
                  </div>
                  
                  {/* Label */}
                  <span className="text-xs font-medium leading-tight">
                    {tab.label}
                  </span>
                  
                  {/* Description */}
                  {tab.description && (
                    <span className={`text-[10px] leading-tight mt-0.5 ${
                      isActive 
                        ? 'text-white/80' 
                        : 'text-slate-500 dark:text-slate-500'
                    }`}>
                      {tab.description}
                    </span>
                  )}
                  
                  {/* Active indicator */}
                  {isActive && (
                    <div
                      className="absolute -top-1 left-1/2 transform -translate-x-1/2 w-1 h-1 rounded-full"
                      style={{ backgroundColor: 'white' }}
                    />
                  )}
                </button>
              );
            })}
          </div>
        </div>
        
        {/* Helper text */}
        <div className="text-center mt-2">
          <p className="text-xs text-slate-500 dark:text-slate-400">
            Choose your preferred authentication method
          </p>
        </div>
      </div>
    </div>
  );
}
