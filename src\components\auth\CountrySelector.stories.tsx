import type { <PERSON><PERSON>, StoryObj } from '@storybook/react';
import { useState } from 'react';
import { CountrySelector, type Country } from './CountrySelector';

const meta: Meta<typeof CountrySelector> = {
  title: 'Auth/CountrySelector',
  component: CountrySelector,
  parameters: {
    layout: 'centered',
    docs: {
      description: {
        component: 'A searchable country selector with flags, names, and dial codes.',
      },
    },
  },
  argTypes: {
    placeholder: {
      control: 'text',
      description: 'Placeholder text for the selector',
    },
    disabled: {
      control: 'boolean',
      description: 'Disable the selector',
    },
    required: {
      control: 'boolean',
      description: 'Mark the field as required',
    },
    label: {
      control: 'text',
      description: 'Label for the selector',
    },
    error: {
      control: 'text',
      description: 'Error message to display',
    },
  },
};

export default meta;
type Story = StoryObj<typeof CountrySelector>;

const InteractiveWrapper = ({ initialValue, ...args }: any) => {
  const [selectedCountry, setSelectedCountry] = useState<Country | null>(initialValue || null);
  
  return (
    <div className="w-80 p-6 bg-white dark:bg-slate-900 rounded-lg">
      <CountrySelector
        value={selectedCountry}
        onChange={setSelectedCountry}
        {...args}
      />
      {selectedCountry && (
        <div className="mt-4 p-3 bg-slate-50 dark:bg-slate-800 rounded-lg">
          <h4 className="font-semibold text-slate-900 dark:text-slate-100 mb-2">
            Selected Country:
          </h4>
          <div className="space-y-1 text-sm">
            <div className="flex items-center gap-2">
              <span className="text-lg">{selectedCountry.flag}</span>
              <span className="font-medium text-slate-900 dark:text-slate-100">
                {selectedCountry.name}
              </span>
            </div>
            <div className="text-slate-600 dark:text-slate-400">
              Code: {selectedCountry.code}
            </div>
            <div className="text-slate-600 dark:text-slate-400">
              Dial Code: {selectedCountry.dialCode}
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export const Default: Story = {
  render: (args) => <InteractiveWrapper {...args} />,
};

export const WithLabel: Story = {
  args: {
    label: 'Country',
    placeholder: 'Choose your country...',
  },
  render: (args) => <InteractiveWrapper {...args} />,
};

export const Required: Story = {
  args: {
    label: 'Country',
    required: true,
    placeholder: 'Select country *',
  },
  render: (args) => <InteractiveWrapper {...args} />,
};

export const WithError: Story = {
  args: {
    label: 'Country',
    error: 'Please select a valid country',
    placeholder: 'Select country...',
  },
  render: (args) => <InteractiveWrapper {...args} />,
};

export const Disabled: Story = {
  args: {
    label: 'Country',
    disabled: true,
    placeholder: 'Country selection disabled',
  },
  render: (args) => <InteractiveWrapper {...args} />,
};

export const PreSelected: Story = {
  args: {
    label: 'Country',
    placeholder: 'Select country...',
  },
  render: (args) => (
    <InteractiveWrapper 
      initialValue={{ code: 'US', name: 'United States', dialCode: '+1', flag: '🇺🇸' }}
      {...args} 
    />
  ),
};

export const CustomPlaceholder: Story = {
  args: {
    label: 'Select Your Location',
    placeholder: 'Where are you located?',
  },
  render: (args) => <InteractiveWrapper {...args} />,
};

export const InForm: Story = {
  render: (args) => (
    <div className="w-96 p-6 bg-white dark:bg-slate-900 rounded-lg">
      <h3 className="text-lg font-semibold text-slate-900 dark:text-slate-100 mb-6">
        Registration Form
      </h3>
      <form className="space-y-4">
        <div>
          <label className="block text-sm font-medium text-slate-700 dark:text-slate-300 mb-2">
            Full Name *
          </label>
          <input
            type="text"
            placeholder="Enter your full name"
            className="w-full px-3 py-2 border-2 border-slate-300 dark:border-slate-600 rounded-lg bg-white dark:bg-slate-700 text-slate-900 dark:text-slate-100 focus:ring-2 focus:ring-blue-500 focus:border-transparent"
          />
        </div>
        
        <div>
          <label className="block text-sm font-medium text-slate-700 dark:text-slate-300 mb-2">
            Email *
          </label>
          <input
            type="email"
            placeholder="Enter your email"
            className="w-full px-3 py-2 border-2 border-slate-300 dark:border-slate-600 rounded-lg bg-white dark:bg-slate-700 text-slate-900 dark:text-slate-100 focus:ring-2 focus:ring-blue-500 focus:border-transparent"
          />
        </div>
        
        <InteractiveWrapper 
          label="Country"
          required={true}
          placeholder="Select your country..."
          {...args} 
        />
        
        <div>
          <label className="block text-sm font-medium text-slate-700 dark:text-slate-300 mb-2">
            Phone Number *
          </label>
          <input
            type="tel"
            placeholder="Enter your phone number"
            className="w-full px-3 py-2 border-2 border-slate-300 dark:border-slate-600 rounded-lg bg-white dark:bg-slate-700 text-slate-900 dark:text-slate-100 focus:ring-2 focus:ring-blue-500 focus:border-transparent"
          />
        </div>
        
        <button
          type="submit"
          className="w-full bg-blue-600 hover:bg-blue-700 text-white font-medium py-2 px-4 rounded-lg transition-colors"
        >
          Register
        </button>
      </form>
    </div>
  ),
};

export const SearchDemo: Story = {
  args: {
    label: 'Country Search Demo',
    placeholder: 'Try searching for "United", "Germany", or "+44"...',
  },
  render: (args) => (
    <div className="w-80 p-6 bg-white dark:bg-slate-900 rounded-lg">
      <div className="mb-4">
        <h3 className="text-lg font-semibold text-slate-900 dark:text-slate-100 mb-2">
          Search Capabilities
        </h3>
        <p className="text-sm text-slate-600 dark:text-slate-400">
          You can search by:
        </p>
        <ul className="text-sm text-slate-600 dark:text-slate-400 mt-1 space-y-1">
          <li>• Country name (e.g., "Germany")</li>
          <li>• Country code (e.g., "US")</li>
          <li>• Dial code (e.g., "+44")</li>
        </ul>
      </div>
      <InteractiveWrapper {...args} />
    </div>
  ),
};
