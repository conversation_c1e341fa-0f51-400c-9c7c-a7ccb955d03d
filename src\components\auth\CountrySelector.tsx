import React, { useMemo } from 'react';
import Select, { StylesConfig, components } from 'react-select';
import { useThemeStore } from '../../stores/themeStore';

export interface Country {
  code: string;
  name: string;
  dialCode: string;
  flag: string;
}

export interface CountrySelectorProps {
  value?: Country | null;
  onChange: (country: Country | null) => void;
  placeholder?: string;
  disabled?: boolean;
  error?: string;
  label?: string;
  required?: boolean;
  className?: string;
  'data-testid'?: string;
}

// Comprehensive list of countries with dial codes (sorted alphabetically)
const countries: Country[] = [
  { code: 'AE', name: 'United Arab Emirates', dialCode: '+971', flag: '🇦🇪' },
  { code: 'AR', name: 'Argentina', dialCode: '+54', flag: '🇦🇷' },
  { code: 'AT', name: 'Austria', dialCode: '+43', flag: '🇦🇹' },
  { code: 'AU', name: 'Australia', dialCode: '+61', flag: '🇦🇺' },
  { code: 'BE', name: 'Belgium', dialCode: '+32', flag: '🇧🇪' },
  { code: 'BR', name: 'Brazil', dialCode: '+55', flag: '🇧🇷' },
  { code: 'CA', name: 'Canada', dialCode: '+1', flag: '🇨🇦' },
  { code: 'CH', name: 'Switzerland', dialCode: '+41', flag: '🇨🇭' },
  { code: 'CL', name: 'Chile', dialCode: '+56', flag: '🇨🇱' },
  { code: 'CN', name: 'China', dialCode: '+86', flag: '🇨🇳' },
  { code: 'CO', name: 'Colombia', dialCode: '+57', flag: '🇨🇴' },
  { code: 'CZ', name: 'Czech Republic', dialCode: '+420', flag: '🇨🇿' },
  { code: 'DE', name: 'Germany', dialCode: '+49', flag: '🇩🇪' },
  { code: 'DK', name: 'Denmark', dialCode: '+45', flag: '🇩🇰' },
  { code: 'EG', name: 'Egypt', dialCode: '+20', flag: '🇪🇬' },
  { code: 'ES', name: 'Spain', dialCode: '+34', flag: '🇪🇸' },
  { code: 'FI', name: 'Finland', dialCode: '+358', flag: '🇫🇮' },
  { code: 'FR', name: 'France', dialCode: '+33', flag: '🇫🇷' },
  { code: 'GB', name: 'United Kingdom', dialCode: '+44', flag: '🇬🇧' },
  { code: 'GR', name: 'Greece', dialCode: '+30', flag: '🇬🇷' },
  { code: 'HK', name: 'Hong Kong', dialCode: '+852', flag: '🇭🇰' },
  { code: 'HU', name: 'Hungary', dialCode: '+36', flag: '🇭🇺' },
  { code: 'ID', name: 'Indonesia', dialCode: '+62', flag: '🇮🇩' },
  { code: 'IE', name: 'Ireland', dialCode: '+353', flag: '🇮🇪' },
  { code: 'IL', name: 'Israel', dialCode: '+972', flag: '🇮🇱' },
  { code: 'IN', name: 'India', dialCode: '+91', flag: '🇮🇳' },
  { code: 'IT', name: 'Italy', dialCode: '+39', flag: '🇮🇹' },
  { code: 'JP', name: 'Japan', dialCode: '+81', flag: '🇯🇵' },
  { code: 'KE', name: 'Kenya', dialCode: '+254', flag: '🇰🇪' },
  { code: 'KR', name: 'South Korea', dialCode: '+82', flag: '🇰🇷' },
  { code: 'LU', name: 'Luxembourg', dialCode: '+352', flag: '🇱🇺' },
  { code: 'MA', name: 'Morocco', dialCode: '+212', flag: '🇲🇦' },
  { code: 'MX', name: 'Mexico', dialCode: '+52', flag: '🇲🇽' },
  { code: 'MY', name: 'Malaysia', dialCode: '+60', flag: '🇲🇾' },
  { code: 'NG', name: 'Nigeria', dialCode: '+234', flag: '🇳🇬' },
  { code: 'NL', name: 'Netherlands', dialCode: '+31', flag: '🇳🇱' },
  { code: 'NO', name: 'Norway', dialCode: '+47', flag: '🇳🇴' },
  { code: 'NZ', name: 'New Zealand', dialCode: '+64', flag: '🇳🇿' },
  { code: 'PE', name: 'Peru', dialCode: '+51', flag: '🇵🇪' },
  { code: 'PH', name: 'Philippines', dialCode: '+63', flag: '🇵🇭' },
  { code: 'PL', name: 'Poland', dialCode: '+48', flag: '🇵🇱' },
  { code: 'PT', name: 'Portugal', dialCode: '+351', flag: '🇵🇹' },
  { code: 'RU', name: 'Russia', dialCode: '+7', flag: '🇷🇺' },
  { code: 'SA', name: 'Saudi Arabia', dialCode: '+966', flag: '🇸🇦' },
  { code: 'SE', name: 'Sweden', dialCode: '+46', flag: '🇸🇪' },
  { code: 'SG', name: 'Singapore', dialCode: '+65', flag: '🇸🇬' },
  { code: 'TH', name: 'Thailand', dialCode: '+66', flag: '🇹🇭' },
  { code: 'TR', name: 'Turkey', dialCode: '+90', flag: '🇹🇷' },
  { code: 'TW', name: 'Taiwan', dialCode: '+886', flag: '🇹🇼' },
  { code: 'UA', name: 'Ukraine', dialCode: '+380', flag: '🇺🇦' },
  { code: 'US', name: 'United States', dialCode: '+1', flag: '🇺🇸' },
  { code: 'VN', name: 'Vietnam', dialCode: '+84', flag: '🇻🇳' },
  { code: 'ZA', name: 'South Africa', dialCode: '+27', flag: '🇿🇦' },
];

// Custom option component
const Option = (props: any) => {
  const { data } = props;
  return (
    <components.Option {...props}>
      <div className="flex items-center gap-3">
        <span className="text-lg">{data.flag}</span>
        <div className="flex-1">
          <div className="font-medium">{data.name}</div>
          <div className="text-sm text-slate-500">{data.dialCode}</div>
        </div>
      </div>
    </components.Option>
  );
};

// Custom single value component
const SingleValue = (props: any) => {
  const { data } = props;
  return (
    <components.SingleValue {...props}>
      <div className="flex items-center gap-2">
        <span className="text-lg">{data.flag}</span>
        <span className="font-medium">{data.name}</span>
        <span className="text-slate-500">({data.dialCode})</span>
      </div>
    </components.SingleValue>
  );
};

export function CountrySelector({
  value,
  onChange,
  placeholder = 'Select country...',
  disabled = false,
  error,
  label,
  required = false,
  className = '',
  'data-testid': testId,
}: CountrySelectorProps) {
  const { colors } = useThemeStore();

  const customStyles: StylesConfig<Country, false> = useMemo(() => ({
    control: (provided, state) => ({
      ...provided,
      backgroundColor: colors.input,
      borderColor: error ? colors.error : state.isFocused ? colors.primary : colors.border,
      borderWidth: '2px',
      borderRadius: '8px',
      minHeight: '44px',
      boxShadow: state.isFocused ? `0 0 0 3px ${colors.ring}20` : 'none',
      '&:hover': {
        borderColor: error ? colors.error : colors.primary,
      },
    }),
    valueContainer: (provided) => ({
      ...provided,
      padding: '8px 12px',
    }),
    input: (provided) => ({
      ...provided,
      color: colors.text,
      margin: 0,
    }),
    placeholder: (provided) => ({
      ...provided,
      color: colors.textMuted,
    }),
    singleValue: (provided) => ({
      ...provided,
      color: colors.text,
    }),
    menu: (provided) => ({
      ...provided,
      backgroundColor: colors.popover,
      border: `1px solid ${colors.border}`,
      borderRadius: '8px',
      boxShadow: `0 4px 12px ${colors.shadow}20`,
      zIndex: 50,
    }),
    menuList: (provided) => ({
      ...provided,
      padding: '4px',
    }),
    option: (provided, state) => ({
      ...provided,
      backgroundColor: state.isSelected 
        ? colors.primary 
        : state.isFocused 
          ? colors.hover 
          : 'transparent',
      color: state.isSelected ? colors.primaryForeground : colors.text,
      borderRadius: '6px',
      margin: '2px 0',
      padding: '8px 12px',
      cursor: 'pointer',
      '&:active': {
        backgroundColor: state.isSelected ? colors.primary : colors.active,
      },
    }),
    indicatorSeparator: () => ({
      display: 'none',
    }),
    dropdownIndicator: (provided, state) => ({
      ...provided,
      color: colors.textMuted,
      transform: state.selectProps.menuIsOpen ? 'rotate(180deg)' : 'rotate(0deg)',
      transition: 'transform 0.2s ease',
      '&:hover': {
        color: colors.text,
      },
    }),
  }), [colors, error]);

  const filterOption = (option: any, inputValue: string) => {
    const searchValue = inputValue.toLowerCase();
    return (
      option.data.name.toLowerCase().includes(searchValue) ||
      option.data.code.toLowerCase().includes(searchValue) ||
      option.data.dialCode.includes(searchValue)
    );
  };

  return (
    <div className={className} data-testid={testId}>
      {label && (
        <label className="block text-sm font-medium text-slate-700 dark:text-slate-300 mb-2">
          {label}
          {required && <span className="text-red-500 ml-1">*</span>}
        </label>
      )}
      
      <Select<Country>
        value={value}
        onChange={onChange}
        options={countries}
        styles={customStyles}
        components={{ Option, SingleValue }}
        placeholder={placeholder}
        isDisabled={disabled}
        isSearchable
        filterOption={filterOption}
        noOptionsMessage={({ inputValue }) => 
          inputValue ? `No countries found for "${inputValue}"` : 'No countries available'
        }
        getOptionLabel={(option) => option.name}
        getOptionValue={(option) => option.code}
        menuPortalTarget={document.body}
        menuPosition="fixed"
        classNamePrefix="country-select"
      />
      
      {error && (
        <p className="mt-2 text-sm text-red-600 dark:text-red-400">
          {error}
        </p>
      )}
    </div>
  );
}
