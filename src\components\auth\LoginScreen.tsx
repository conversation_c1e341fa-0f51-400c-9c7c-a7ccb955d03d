import { useState } from 'react';
import { useThemeStore } from '../../stores/themeStore';
import { GlobalThemeToggle } from '../GlobalThemeToggle';
import { ThemeToggle } from '../ThemeToggle';
import { LoginForm } from './LoginForm';
import { OTPForm } from './OTPForm';
import { ForgotPasswordForm } from './ForgotPasswordForm';
import { AccessRequestForm } from './AccessRequestForm';
import { SocialLoginButtons, SocialLoginDivider } from './SocialLoginButtons';
import { CaptchaComponent } from '../security/CaptchaComponent';
import { DevLoginModal } from './DevLoginModal';
import { ERPFeaturesSlider } from './ERPFeaturesSlider';
import { AuthNavigationTabs } from './AuthNavigationTabs';
import { VerificationMethodButtons } from './VerificationMethodButtons';

export interface LoginScreenProps {
  className?: string;
  onLogin?: (credentials: any) => void;
  onAccessRequest?: (request: any) => void;
  'data-testid'?: string;
}

export type LoginMode = 'login' | 'otp' | 'forgot' | 'access-request';

export function LoginScreen({
  className = '',
  onLogin,
  onAccessRequest,
  'data-testid': testId,
}: LoginScreenProps) {
  const { colors } = useThemeStore();
  const [isLeftSide, setIsLeftSide] = useState(true);
  const [loginMode, setLoginMode] = useState<LoginMode>('login');
  const [isAnimating, setIsAnimating] = useState(false);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState('');
  const [showCaptcha, setShowCaptcha] = useState(false);
  const [captchaVerified, setCaptchaVerified] = useState(false);
  const [otpSent, setOtpSent] = useState(false);
  const [forgotPasswordStep, setForgotPasswordStep] = useState<
    'method' | 'otp' | 'reset'
  >('method');
  const [showSocialLogin, setShowSocialLogin] = useState(false);
  const [showDevLogin, setShowDevLogin] = useState(false);
  const [selectedVerificationMethod, setSelectedVerificationMethod] = useState<'whatsapp' | 'sms' | 'call'>('whatsapp');

  const handleSideSwap = () => {
    if (isAnimating) return;
    setIsAnimating(true);
    setTimeout(() => {
      setIsLeftSide(!isLeftSide);
      setIsAnimating(false);
    }, 400);
  };

  const handleLogin = async (credentials: any) => {
    setLoading(true);
    setError('');

    // Simulate API call
    try {
      await new Promise(resolve => setTimeout(resolve, 1500));
      onLogin?.(credentials);
    } catch (err) {
      setError('Login failed. Please try again.');
      setShowCaptcha(true);
    } finally {
      setLoading(false);
    }
  };

  const handleSocialLogin = (provider: string) => {
    setLoading(true);
    setError('');

    // Simulate social login
    setTimeout(() => {
      setLoading(false);
      console.log(`${provider} login initiated`);
    }, 1000);
  };

  const handleOTPSend = async (_phone: string, _method: 'whatsapp' | 'sms') => {
    setLoading(true);
    setError('');

    try {
      await new Promise(resolve => setTimeout(resolve, 1000));
      setOtpSent(true);
    } catch (err) {
      setError('Failed to send OTP. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  const handleForgotPassword = async (data: { method: 'whatsapp' | 'email' | 'mobile'; value: string; }) => {
    setLoading(true);
    setError('');

    try {
      await new Promise(resolve => setTimeout(resolve, 1000));
      setForgotPasswordStep('otp');
    } catch (err) {
      setError('Failed to send reset code. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  const handleAccessRequest = async (data: any) => {
    setLoading(true);
    setError('');

    try {
      await new Promise(resolve => setTimeout(resolve, 2000));
      onAccessRequest?.(data);
    } catch (err) {
      setError('Failed to submit request. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  const BrandSection = () => (
    <div className="relative h-full flex flex-col justify-center items-center p-8 lg:p-12">
      {/* Background Pattern */}
      <div className="absolute inset-0 opacity-5">
        <svg
          className="w-full h-full"
          viewBox="0 0 100 100"
          xmlns="http://www.w3.org/2000/svg"
        >
          <defs>
            <pattern
              id="grid"
              width="10"
              height="10"
              patternUnits="userSpaceOnUse"
            >
              <path
                d="M 10 0 L 0 0 0 10"
                fill="none"
                stroke="currentColor"
                strokeWidth="0.5"
              />
            </pattern>
          </defs>
          <rect width="100" height="100" fill="url(#grid)" />
        </svg>
      </div>

      {/* Brand Content */}
      <div className="relative z-10 text-center max-w-md">
        {/* Logo */}
        <div className="mb-8">
          <div
            className="w-20 h-20 mx-auto rounded-2xl flex items-center justify-center text-white text-2xl font-bold"
            style={{ backgroundColor: colors.primary }}
          >
            N
          </div>
        </div>

        {/* Brand Message */}
        <h1 className="text-3xl lg:text-4xl font-bold text-white mb-4">
          Welcome to Nexed
        </h1>
        <p className="text-lg text-white/80 mb-8">
          Your comprehensive ERP solution for modern business management
        </p>

        {/* Features */}
        <div className="space-y-4 text-left">
          {[
            'Streamlined Business Operations',
            'Real-time Analytics & Reporting',
            'Secure Cloud Infrastructure',
            'Mobile-First Design',
          ].map((feature, index) => (
            <div key={index} className="flex items-center text-white/70">
              <div
                className="w-2 h-2 rounded-full mr-3"
                style={{ backgroundColor: colors.accent }}
              />
              <span>{feature}</span>
            </div>
          ))}
        </div>
      </div>

      {/* Animated Elements */}
      <div className="absolute top-10 right-10 w-32 h-32 opacity-10">
        <div
          className="w-full h-full rounded-full animate-pulse"
          style={{ backgroundColor: colors.secondary }}
        />
      </div>
      <div className="absolute bottom-20 left-10 w-24 h-24 opacity-10">
        <div
          className="w-full h-full rounded-full animate-bounce"
          style={{ backgroundColor: colors.accent }}
        />
      </div>
    </div>
  );

  const FormSection = () => (
    <div className="relative h-full flex flex-col justify-center p-8 lg:p-12">
      {/* Header */}
      <div className="mb-8">
        <div className="flex justify-between items-center mb-6">
          <h2 className="text-2xl lg:text-3xl font-bold text-slate-900 dark:text-slate-100">
            {loginMode === 'login' && 'Sign In'}
            {loginMode === 'otp' && 'Verify OTP'}
            {loginMode === 'forgot' && 'Reset Password'}
            {loginMode === 'access-request' && 'Request Access'}
          </h2>
          <ThemeToggle size="md" />
        </div>
        <p className="text-slate-600 dark:text-slate-400">
          {loginMode === 'login' &&
            'Enter your credentials to access your account'}
          {loginMode === 'otp' &&
            'Enter the verification code sent to your device'}
          {loginMode === 'forgot' && 'Enter your email to reset your password'}
          {loginMode === 'access-request' &&
            'Fill out the form below to request access'}
        </p>
      </div>

      {/* Form Content */}
      <div className="flex-1 max-w-md mx-auto w-full">
        <div
          className={`transition-all duration-300 ${isAnimating ? 'opacity-0 scale-95' : 'opacity-100 scale-100'}`}
        >
          {loginMode === 'login' && (
            <>
              <LoginForm
                onSubmit={handleLogin}
                onForgotPassword={() => setLoginMode('forgot')}
                onSwitchToOTP={() => setLoginMode('otp')}
                loading={loading}
                error={error}
              />

              {showCaptcha && !captchaVerified && (
                <div className="mt-6">
                  <CaptchaComponent
                    onVerify={_token => {
                      setCaptchaVerified(true);
                      setShowCaptcha(false);
                    }}
                    onError={error => setError(error)}
                  />
                </div>
              )}

              {/* Collapsible Social Login */}
              <div className="mt-6">
                <button
                  type="button"
                  onClick={() => setShowSocialLogin(!showSocialLogin)}
                  className="w-full flex items-center justify-center gap-2 py-3 px-4 border border-gray-300 dark:border-gray-600 rounded-lg text-sm font-medium text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-800 transition-colors"
                >
                  <svg
                    className={`w-4 h-4 transition-transform ${showSocialLogin ? 'rotate-180' : ''}`}
                    fill="none"
                    stroke="currentColor"
                    viewBox="0 0 24 24"
                  >
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth={2}
                      d="M19 9l-7 7-7-7"
                    />
                  </svg>
                  {showSocialLogin
                    ? 'Hide Social Login'
                    : 'Show Social Login Options'}
                </button>

                {showSocialLogin && (
                  <div className="mt-4 space-y-4">
                    <SocialLoginDivider />
                    <SocialLoginButtons
                      layout="grid"
                      showLabels={false}
                      loading={loading}
                      onGoogleLogin={() => handleSocialLogin('Google')}
                      onFacebookLogin={() => handleSocialLogin('Facebook')}
                      onLinkedInLogin={() => handleSocialLogin('LinkedIn')}
                      onMicrosoftLogin={() => handleSocialLogin('Microsoft')}
                    />
                  </div>
                )}
              </div>
            </>
          )}

          {loginMode === 'otp' && (
            <OTPForm
              onSubmit={data => console.log('OTP Login:', data)}
              onSendOTP={handleOTPSend}
              onBack={() => setLoginMode('login')}
              loading={loading}
              error={error}
              otpSent={otpSent}
            />
          )}

          {loginMode === 'forgot' && (
            <ForgotPasswordForm
              onSubmit={handleForgotPassword}
              onBack={() => setLoginMode('login')}
              onVerifyOTP={data => console.log('Password Reset:', data)}
              loading={loading}
              error={error}
              step={forgotPasswordStep}
            />
          )}

          {loginMode === 'access-request' && (
            <AccessRequestForm
              onSubmit={handleAccessRequest}
              onBack={() => setLoginMode('login')}
              loading={loading}
              error={error}
            />
          )}
        </div>
      </div>

      {/* Mode Switcher */}
      <div className="mt-8 text-center">
        <div className="flex flex-wrap justify-center gap-4 text-sm">
          <button
            onClick={() => setLoginMode('login')}
            className={`px-3 py-1 rounded transition-colors ${
              loginMode === 'login'
                ? 'text-white'
                : 'text-slate-600 dark:text-slate-400 hover:text-slate-800 dark:hover:text-slate-200'
            }`}
            style={{
              backgroundColor:
                loginMode === 'login' ? colors.primary : 'transparent',
            }}
          >
            Sign In
          </button>
          <button
            onClick={() => setLoginMode('otp')}
            className={`px-3 py-1 rounded transition-colors ${
              loginMode === 'otp'
                ? 'text-white'
                : 'text-slate-600 dark:text-slate-400 hover:text-slate-800 dark:hover:text-slate-200'
            }`}
            style={{
              backgroundColor:
                loginMode === 'otp' ? colors.primary : 'transparent',
            }}
          >
            OTP Login
          </button>
          <button
            onClick={() => setLoginMode('forgot')}
            className={`px-3 py-1 rounded transition-colors ${
              loginMode === 'forgot'
                ? 'text-white'
                : 'text-slate-600 dark:text-slate-400 hover:text-slate-800 dark:hover:text-slate-200'
            }`}
            style={{
              backgroundColor:
                loginMode === 'forgot' ? colors.primary : 'transparent',
            }}
          >
            Forgot Password
          </button>
          <button
            onClick={() => setLoginMode('access-request')}
            className={`px-3 py-1 rounded transition-colors ${
              loginMode === 'access-request'
                ? 'text-white'
                : 'text-slate-600 dark:text-slate-400 hover:text-slate-800 dark:hover:text-slate-200'
            }`}
            style={{
              backgroundColor:
                loginMode === 'access-request' ? colors.primary : 'transparent',
            }}
          >
            Request Access
          </button>
        </div>
      </div>
    </div>
  );

  return (
    <div className={`min-h-screen flex ${className}`} data-testid={testId}>
      {/* Global Theme Toggle */}
      <GlobalThemeToggle position="top-right" />

      {/* Side Swap Button */}
      <button
        onClick={handleSideSwap}
        disabled={isAnimating || loading}
        className={`
          fixed top-6 left-6 z-50
          p-4 rounded-full
          bg-white/10 backdrop-blur-md
          border border-white/30
          text-white
          hover:bg-white/20 hover:border-white/40 hover:scale-110
          active:scale-95
          transition-all duration-300
          disabled:opacity-50 disabled:cursor-not-allowed
          shadow-lg hover:shadow-xl
          group
        `}
        aria-label="Switch layout sides"
        title="Switch layout sides"
      >
        <svg
          className={`w-6 h-6 transition-transform duration-500 ${isAnimating ? 'rotate-180 scale-75' : 'rotate-0 scale-100'} group-hover:scale-110`}
          fill="none"
          stroke="currentColor"
          viewBox="0 0 24 24"
        >
          <path
            strokeLinecap="round"
            strokeLinejoin="round"
            strokeWidth={2}
            d="M8 7h12m0 0l-4-4m4 4l-4 4m0 6H4m0 0l4 4m0-4l-4-4"
          />
        </svg>

        {/* Ripple effect */}
        <div
          className={`
            absolute inset-0 rounded-full
            bg-white/20
            transition-all duration-300
            ${isAnimating ? 'scale-150 opacity-0' : 'scale-0 opacity-0'}
          `}
        />
      </button>

      {/* Left Side - ERP Features or Form */}
      <div
        className={`
          w-full lg:w-1/2
          transition-all
          duration-700
          ease-in-out
          transform-gpu
          ${isAnimating ? 'scale-95 opacity-60 blur-sm' : 'scale-100 opacity-100 blur-0'}
        `}
      >
        <div
          className={`h-full transition-all duration-700 ${isAnimating ? 'translate-x-4' : 'translate-x-0'}`}
        >
          {isLeftSide ? (
            <ERPFeaturesSlider
              autoPlay={true}
              autoPlayInterval={6000}
              showDots={true}
              showArrows={true}
              className="w-full h-full"
            />
          ) : (
            <div className="h-full bg-white dark:bg-slate-900 flex flex-col">
              <FormSection />
              <AuthNavigationTabs
                currentMode={loginMode}
                onModeChange={setLoginMode}
                disabled={loading}
              />
            </div>
          )}
        </div>
      </div>

      {/* Right Side */}
      <div
        className={`
          w-full lg:w-1/2
          transition-all
          duration-700
          ease-in-out
          transform-gpu
          ${isAnimating ? 'scale-95 opacity-60 blur-sm' : 'scale-100 opacity-100 blur-0'}
        `}
        style={{
          background: !isLeftSide
            ? `linear-gradient(135deg, ${colors.primary} 0%, ${colors.secondary} 50%, ${colors.accent} 100%)`
            : colors.background,
          boxShadow: !isLeftSide ? `0 20px 40px ${colors.primary}20` : 'none',
        }}
      >
        <div
          className={`h-full transition-all duration-700 ${isAnimating ? '-translate-x-4' : 'translate-x-0'}`}
        >
          {!isLeftSide ? <BrandSection /> : <FormSection />}
        </div>
      </div>

      {/* Dev Login Button - Only show in development */}
      {import.meta.env.DEV && (
        <button
          onClick={() => setShowDevLogin(true)}
          className="fixed bottom-6 left-6 z-50 px-4 py-2 bg-yellow-500 hover:bg-yellow-600 text-black font-medium rounded-lg shadow-lg transition-colors"
          title="Development Login Options"
        >
          🔧 Dev Login
        </button>
      )}

      {/* Dev Login Modal */}
      {showDevLogin && (
        <DevLoginModal
          isOpen={showDevLogin}
          onClose={() => setShowDevLogin(false)}
          onLogin={user => {
            setShowDevLogin(false);
            onLogin?.(user);
          }}
        />
      )}
    </div>
  );
}
