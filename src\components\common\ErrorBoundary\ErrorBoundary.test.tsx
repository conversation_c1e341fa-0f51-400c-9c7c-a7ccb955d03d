/**
 * ErrorBoundary Component Tests
 * Comprehensive test suite for the ErrorBoundary component
 */

import React from 'react';
import { render, screen, waitFor } from '@testing-library/react';
import { vi, describe, it, expect, beforeEach, afterEach } from 'vitest';
import ErrorBoundary from './ErrorBoundary';
import * as errorReporting from '../../../utils/errorReporting';
import * as errorRecovery from '../../../utils/errorRecovery';

// Mock the utility modules
vi.mock('../../../utils/errorReporting');
vi.mock('../../../utils/errorRecovery');
vi.mock('../../../stores/themeStore', () => ({
  useThemeStore: () => ({
    colors: {
      primary: '#3b82f6',
      secondary: '#6b7280',
      error: '#ef4444',
      warning: '#f59e0b',
      text: '#1f2937',
      textSecondary: '#6b7280',
      primaryText: '#ffffff',
      secondaryText: '#ffffff',
      border: '#d1d5db'
    },
    theme: 'light'
  })
}));

const mockReportError = vi.mocked(errorReporting.reportError);
const mockExecuteAutoRecovery = vi.mocked(errorRecovery.executeAutoRecovery);

// Component that throws an error
const ErrorThrowingComponent: React.FC<{ shouldThrow?: boolean; errorMessage?: string }> = ({ 
  shouldThrow = false, 
  errorMessage = 'Test error' 
}) => {
  if (shouldThrow) {
    throw new Error(errorMessage);
  }
  return <div data-testid="working-component">Component is working</div>;
};

// Component that works normally
const WorkingComponent: React.FC = () => {
  return <div data-testid="working-component">Component is working</div>;
};

describe('ErrorBoundary', () => {
  beforeEach(() => {
    vi.clearAllMocks();
    mockReportError.mockResolvedValue(undefined);
    mockExecuteAutoRecovery.mockResolvedValue(false);
    
    // Suppress console.error for cleaner test output
    vi.spyOn(console, 'error').mockImplementation(() => {});
  });

  afterEach(() => {
    vi.restoreAllMocks();
  });

  describe('Normal Operation', () => {
    it('renders children when no error occurs', () => {
      render(
        <ErrorBoundary>
          <WorkingComponent />
        </ErrorBoundary>
      );

      expect(screen.getByTestId('working-component')).toBeInTheDocument();
      expect(screen.getByText('Component is working')).toBeInTheDocument();
    });

    it('passes through props to children', () => {
      render(
        <ErrorBoundary data-testid="error-boundary">
          <WorkingComponent />
        </ErrorBoundary>
      );

      expect(screen.getByTestId('working-component')).toBeInTheDocument();
    });
  });

  describe('Error Handling', () => {
    it('catches and displays error when child component throws', () => {
      render(
        <ErrorBoundary componentName="TestComponent">
          <ErrorThrowingComponent shouldThrow={true} errorMessage="Test error message" />
        </ErrorBoundary>
      );

      expect(screen.queryByTestId('working-component')).not.toBeInTheDocument();
      expect(screen.getByText('Component Error')).toBeInTheDocument();
      expect(screen.getByText(/an unexpected error occurred/i)).toBeInTheDocument();
    });

    it('calls custom onError handler when provided', async () => {
      const onErrorMock = vi.fn();
      
      render(
        <ErrorBoundary onError={onErrorMock}>
          <ErrorThrowingComponent shouldThrow={true} />
        </ErrorBoundary>
      );

      await waitFor(() => {
        expect(onErrorMock).toHaveBeenCalledWith(
          expect.objectContaining({
            message: 'Test error',
            type: expect.any(String)
          }),
          expect.objectContaining({
            componentStack: expect.any(String)
          })
        );
      });
    });

    it('reports error when enableReporting is true', async () => {
      render(
        <ErrorBoundary enableReporting={true} componentName="TestComponent">
          <ErrorThrowingComponent shouldThrow={true} />
        </ErrorBoundary>
      );

      await waitFor(() => {
        expect(mockReportError).toHaveBeenCalledWith(
          expect.objectContaining({
            message: 'Test error'
          }),
          expect.objectContaining({
            componentName: 'TestComponent'
          })
        );
      });
    });

    it('does not report error when enableReporting is false', async () => {
      render(
        <ErrorBoundary enableReporting={false}>
          <ErrorThrowingComponent shouldThrow={true} />
        </ErrorBoundary>
      );

      // Wait a bit to ensure no reporting happens
      await new Promise(resolve => setTimeout(resolve, 100));
      expect(mockReportError).not.toHaveBeenCalled();
    });

    it('attempts auto recovery when enableAutoRecovery is true', async () => {
      render(
        <ErrorBoundary enableAutoRecovery={true} componentName="TestComponent">
          <ErrorThrowingComponent shouldThrow={true} />
        </ErrorBoundary>
      );

      await waitFor(() => {
        expect(mockExecuteAutoRecovery).toHaveBeenCalledWith(
          expect.objectContaining({
            error: expect.objectContaining({
              message: 'Test error'
            }),
            component: 'TestComponent'
          })
        );
      });
    });

    it('does not attempt auto recovery when enableAutoRecovery is false', async () => {
      render(
        <ErrorBoundary enableAutoRecovery={false}>
          <ErrorThrowingComponent shouldThrow={true} />
        </ErrorBoundary>
      );

      // Wait a bit to ensure no auto recovery happens
      await new Promise(resolve => setTimeout(resolve, 100));
      expect(mockExecuteAutoRecovery).not.toHaveBeenCalled();
    });
  });

  describe('Error Boundary Levels', () => {
    it('displays correct title for page level', () => {
      render(
        <ErrorBoundary level="page">
          <ErrorThrowingComponent shouldThrow={true} />
        </ErrorBoundary>
      );

      expect(screen.getByText('Application Error')).toBeInTheDocument();
    });

    it('displays correct title for section level', () => {
      render(
        <ErrorBoundary level="section">
          <ErrorThrowingComponent shouldThrow={true} />
        </ErrorBoundary>
      );

      expect(screen.getByText('Section Error')).toBeInTheDocument();
    });

    it('displays correct title for component level', () => {
      render(
        <ErrorBoundary level="component">
          <ErrorThrowingComponent shouldThrow={true} />
        </ErrorBoundary>
      );

      expect(screen.getByText('Component Error')).toBeInTheDocument();
    });
  });

  describe('Auto Recovery', () => {
    it('shows recovery indicator during auto recovery', async () => {
      // Mock auto recovery to take some time
      mockExecuteAutoRecovery.mockImplementation(() => 
        new Promise(resolve => setTimeout(() => resolve(false), 100))
      );

      render(
        <ErrorBoundary enableAutoRecovery={true}>
          <ErrorThrowingComponent shouldThrow={true} />
        </ErrorBoundary>
      );

      // Should show recovery indicator initially
      expect(screen.getByText('Attempting to recover...')).toBeInTheDocument();

      // Wait for auto recovery to complete
      await waitFor(() => {
        expect(screen.queryByText('Attempting to recover...')).not.toBeInTheDocument();
      }, { timeout: 200 });
    });

    it('resets error state when auto recovery succeeds', async () => {
      mockExecuteAutoRecovery.mockResolvedValue(true);

      const { rerender } = render(
        <ErrorBoundary enableAutoRecovery={true}>
          <ErrorThrowingComponent shouldThrow={true} />
        </ErrorBoundary>
      );

      // Wait for auto recovery
      await waitFor(() => {
        expect(mockExecuteAutoRecovery).toHaveBeenCalled();
      });

      // Rerender with working component to simulate recovery
      rerender(
        <ErrorBoundary enableAutoRecovery={true}>
          <WorkingComponent />
        </ErrorBoundary>
      );

      expect(screen.getByTestId('working-component')).toBeInTheDocument();
    });
  });

  describe('Reset Functionality', () => {
    it('resets error state when resetKeys change', () => {
      const { rerender } = render(
        <ErrorBoundary resetOnPropsChange={true} resetKeys={['key1']}>
          <ErrorThrowingComponent shouldThrow={true} />
        </ErrorBoundary>
      );

      // Error should be displayed
      expect(screen.getByText('Component Error')).toBeInTheDocument();

      // Change reset keys and rerender with working component
      rerender(
        <ErrorBoundary resetOnPropsChange={true} resetKeys={['key2']}>
          <WorkingComponent />
        </ErrorBoundary>
      );

      // Should show working component
      expect(screen.getByTestId('working-component')).toBeInTheDocument();
    });

    it('does not reset when resetOnPropsChange is false', () => {
      const { rerender } = render(
        <ErrorBoundary resetOnPropsChange={false} resetKeys={['key1']}>
          <ErrorThrowingComponent shouldThrow={true} />
        </ErrorBoundary>
      );

      // Error should be displayed
      expect(screen.getByText('Component Error')).toBeInTheDocument();

      // Change reset keys
      rerender(
        <ErrorBoundary resetOnPropsChange={false} resetKeys={['key2']}>
          <WorkingComponent />
        </ErrorBoundary>
      );

      // Should still show error
      expect(screen.getByText('Component Error')).toBeInTheDocument();
    });
  });

  describe('Custom Fallback Component', () => {
    const CustomFallback: React.FC<any> = ({ error, resetError }) => (
      <div data-testid="custom-fallback">
        Custom error: {error.message}
        <button onClick={resetError}>Custom Reset</button>
      </div>
    );

    it('renders custom fallback component when provided', () => {
      render(
        <ErrorBoundary fallback={CustomFallback}>
          <ErrorThrowingComponent shouldThrow={true} errorMessage="Custom error message" />
        </ErrorBoundary>
      );

      expect(screen.getByTestId('custom-fallback')).toBeInTheDocument();
      expect(screen.getByText('Custom error: Custom error message')).toBeInTheDocument();
      expect(screen.getByRole('button', { name: 'Custom Reset' })).toBeInTheDocument();
    });
  });

  describe('Error Isolation', () => {
    it('catches synchronous render errors when isolateErrors is true', () => {
      const ThrowingComponent = () => {
        throw new Error('Synchronous error');
      };

      render(
        <ErrorBoundary isolateErrors={true}>
          <ThrowingComponent />
        </ErrorBoundary>
      );

      expect(screen.getByText('Component Error')).toBeInTheDocument();
    });
  });

  describe('Component Name and Context', () => {
    it('includes component name in error context', async () => {
      render(
        <ErrorBoundary componentName="SpecificComponent" enableReporting={true}>
          <ErrorThrowingComponent shouldThrow={true} />
        </ErrorBoundary>
      );

      await waitFor(() => {
        expect(mockReportError).toHaveBeenCalledWith(
          expect.objectContaining({
            context: expect.objectContaining({
              component: 'SpecificComponent'
            })
          }),
          expect.any(Object)
        );
      });
    });

    it('includes route information in error context', async () => {
      // Mock window.location
      Object.defineProperty(window, 'location', {
        value: { pathname: '/test-route' },
        writable: true
      });

      render(
        <ErrorBoundary enableReporting={true}>
          <ErrorThrowingComponent shouldThrow={true} />
        </ErrorBoundary>
      );

      await waitFor(() => {
        expect(mockReportError).toHaveBeenCalledWith(
          expect.objectContaining({
            context: expect.objectContaining({
              route: '/test-route'
            })
          }),
          expect.any(Object)
        );
      });
    });
  });

  describe('Data Test ID', () => {
    it('passes data-testid to fallback component', () => {
      render(
        <ErrorBoundary data-testid="test-error-boundary">
          <ErrorThrowingComponent shouldThrow={true} />
        </ErrorBoundary>
      );

      // The data-testid should be passed to the fallback component
      // This would need to be verified based on the actual implementation
      expect(screen.getByText('Component Error')).toBeInTheDocument();
    });
  });
});
