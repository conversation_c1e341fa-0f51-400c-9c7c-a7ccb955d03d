/**
 * ErrorFallback Component
 * Professional UI component for displaying errors with recovery actions and theme integration
 */

import React, { useState, useEffect } from 'react';
import { useThemeStore } from '../../../stores/themeStore';
import type { AppError } from '../../../utils/errorTypes';
import { ErrorSeverity } from '../../../utils/errorTypes';
import type { RecoveryStrategy } from '../../../utils/errorRecovery';
import { getRecoveryStrategies, executeRecoveryStrategy } from '../../../utils/errorRecovery';
import { reportError } from '../../../utils/errorReporting';
import { ThemeToggle } from '../../ThemeToggle';

// Props interface for ErrorFallback
export interface ErrorFallbackProps {
  error: AppError;
  resetError: () => void;
  componentName?: string;
  level?: 'page' | 'section' | 'component';
  showDetails?: boolean;
  enableUserFeedback?: boolean;
  'data-testid'?: string;
}

/**
 * Professional Error Fallback UI Component
 */
const ErrorFallback: React.FC<ErrorFallbackProps> = ({
  error,
  resetError,
  componentName,
  level = 'component',
  showDetails = false,
  enableUserFeedback = true,
  'data-testid': testId
}) => {
  const { colors, theme } = useThemeStore();
  const [showTechnicalDetails, setShowTechnicalDetails] = useState(showDetails);
  const [userFeedback, setUserFeedback] = useState('');
  const [isSubmittingFeedback, setIsSubmittingFeedback] = useState(false);
  const [recoveryStrategies, setRecoveryStrategies] = useState<RecoveryStrategy[]>([]);
  const [isExecutingRecovery, setIsExecutingRecovery] = useState(false);

  // Get recovery strategies on mount
  useEffect(() => {
    const strategies = getRecoveryStrategies({
      error,
      component: componentName,
      route: window.location.pathname
    });
    setRecoveryStrategies(strategies);
  }, [error, componentName]);

  // Get error severity styling
  const getSeverityStyles = () => {
    switch (error.severity) {
      case ErrorSeverity.LOW:
        return {
          borderColor: colors.warning || '#f59e0b',
          backgroundColor: `${colors.warning || '#f59e0b'}10`,
          iconColor: colors.warning || '#f59e0b'
        };
      case ErrorSeverity.MEDIUM:
        return {
          borderColor: colors.warning || '#f59e0b',
          backgroundColor: `${colors.warning || '#f59e0b'}15`,
          iconColor: colors.warning || '#f59e0b'
        };
      case ErrorSeverity.HIGH:
      case ErrorSeverity.CRITICAL:
        return {
          borderColor: colors.error || '#ef4444',
          backgroundColor: `${colors.error || '#ef4444'}10`,
          iconColor: colors.error || '#ef4444'
        };
      default:
        return {
          borderColor: colors.error || '#ef4444',
          backgroundColor: `${colors.error || '#ef4444'}10`,
          iconColor: colors.error || '#ef4444'
        };
    }
  };

  // Get error icon based on severity
  const getErrorIcon = () => {
    switch (error.severity) {
      case ErrorSeverity.LOW:
        return '⚠️';
      case ErrorSeverity.MEDIUM:
        return '⚠️';
      case ErrorSeverity.HIGH:
        return '❌';
      case ErrorSeverity.CRITICAL:
        return '🚨';
      default:
        return '❌';
    }
  };

  // Get container size based on level
  const getContainerClasses = () => {
    const baseClasses = 'rounded-lg border transition-all duration-200';
    
    switch (level) {
      case 'page':
        return `${baseClasses} min-h-screen flex items-center justify-center p-8`;
      case 'section':
        return `${baseClasses} min-h-96 flex items-center justify-center p-6`;
      case 'component':
      default:
        return `${baseClasses} p-4`;
    }
  };

  // Handle recovery strategy execution
  const handleRecoveryAction = async (strategy: RecoveryStrategy) => {
    if (strategy.confirmationRequired) {
      const confirmed = window.confirm(
        `Are you sure you want to ${strategy.label.toLowerCase()}? ${strategy.description}`
      );
      if (!confirmed) return;
    }

    setIsExecutingRecovery(true);

    try {
      await executeRecoveryStrategy(strategy, {
        error,
        component: componentName,
        route: window.location.pathname,
        userInitiated: true
      });

      // If we reach here, the strategy was successful
      // Some strategies (like reload) won't reach this point
      resetError();
    } catch (recoveryError) {
      console.error('Recovery strategy failed:', recoveryError);
      // Show error message to user
      alert(`Recovery action failed: ${recoveryError instanceof Error ? recoveryError.message : 'Unknown error'}`);
    } finally {
      setIsExecutingRecovery(false);
    }
  };

  // Handle user feedback submission
  const handleFeedbackSubmit = async () => {
    if (!userFeedback.trim()) return;

    setIsSubmittingFeedback(true);

    try {
      await reportError(error, {
        userFeedback: userFeedback.trim(),
        component: componentName,
        level,
        feedbackTimestamp: new Date().toISOString()
      });

      setUserFeedback('');
      alert('Thank you for your feedback! We\'ll use it to improve the application.');
    } catch (feedbackError) {
      console.error('Failed to submit feedback:', feedbackError);
      alert('Failed to submit feedback. Please try again later.');
    } finally {
      setIsSubmittingFeedback(false);
    }
  };

  const severityStyles = getSeverityStyles();
  const containerClasses = getContainerClasses();

  return (
    <div
      className={containerClasses}
      style={{
        borderColor: severityStyles.borderColor,
        backgroundColor: severityStyles.backgroundColor
      }}
      data-testid={testId}
    >
      <div className="w-full max-w-2xl mx-auto">
        {/* Theme Toggle - positioned at top right */}
        <div className="flex justify-end mb-4">
          <ThemeToggle />
        </div>

        {/* Error Header */}
        <div className="text-center mb-6">
          <div 
            className="text-4xl mb-4"
            style={{ color: severityStyles.iconColor }}
          >
            {getErrorIcon()}
          </div>
          
          <h2 
            className="text-xl font-bold mb-2"
            style={{ color: colors.text }}
          >
            {level === 'page' ? 'Application Error' : 
             level === 'section' ? 'Section Error' : 
             'Component Error'}
          </h2>
          
          <p 
            className="text-base mb-4"
            style={{ color: colors.textSecondary }}
          >
            {error.userMessage || 'An unexpected error occurred'}
          </p>

          {componentName && (
            <p 
              className="text-sm"
              style={{ color: colors.textSecondary }}
            >
              Component: {componentName}
            </p>
          )}
        </div>

        {/* Recovery Actions */}
        {recoveryStrategies.length > 0 && (
          <div className="mb-6">
            <h3 
              className="text-lg font-semibold mb-3 text-center"
              style={{ color: colors.text }}
            >
              What would you like to do?
            </h3>
            
            <div className="flex flex-wrap gap-3 justify-center">
              {recoveryStrategies.map((strategy, index) => (
                <button
                  key={index}
                  onClick={() => handleRecoveryAction(strategy)}
                  disabled={isExecutingRecovery}
                  className={`
                    px-4 py-2 rounded-lg font-medium transition-all duration-200
                    ${strategy.primary ? 'ring-2' : ''}
                    ${isExecutingRecovery ? 'opacity-50 cursor-not-allowed' : 'hover:opacity-80'}
                  `}
                  style={{
                    backgroundColor: strategy.primary ? colors.primary : colors.secondary,
                    color: strategy.primary ? colors.primaryForeground : colors.secondaryForeground,
                    ...(strategy.primary && { boxShadow: `0 0 0 2px ${colors.ring}` })
                  }}
                >
                  <span className="mr-2">{strategy.icon}</span>
                  {strategy.label}
                </button>
              ))}
            </div>
          </div>
        )}

        {/* Technical Details Toggle */}
        <div className="text-center mb-4">
          <button
            onClick={() => setShowTechnicalDetails(!showTechnicalDetails)}
            className="text-sm underline transition-colors duration-200"
            style={{ color: colors.textSecondary }}
          >
            {showTechnicalDetails ? 'Hide' : 'Show'} Technical Details
          </button>
        </div>

        {/* Technical Details */}
        {showTechnicalDetails && (
          <div 
            className="mb-6 p-4 rounded-lg border text-sm font-mono"
            style={{
              backgroundColor: theme === 'dark' ? '#1f2937' : '#f9fafb',
              borderColor: colors.border,
              color: colors.text
            }}
          >
            <div className="space-y-2">
              <div><strong>Type:</strong> {error.type}</div>
              <div><strong>Severity:</strong> {error.severity}</div>
              <div><strong>Message:</strong> {error.message}</div>
              <div><strong>Timestamp:</strong> {error.timestamp.toLocaleString()}</div>
              {error.context?.route && (
                <div><strong>Route:</strong> {error.context.route}</div>
              )}
              {error.stack && (
                <details className="mt-2">
                  <summary className="cursor-pointer font-bold">Stack Trace</summary>
                  <pre className="mt-2 text-xs overflow-auto max-h-40 whitespace-pre-wrap">
                    {error.stack}
                  </pre>
                </details>
              )}
            </div>
          </div>
        )}

        {/* User Feedback */}
        {enableUserFeedback && (
          <div className="border-t pt-4" style={{ borderColor: colors.border }}>
            <h4 
              className="text-sm font-semibold mb-2"
              style={{ color: colors.text }}
            >
              Help us improve (optional)
            </h4>
            
            <div className="space-y-3">
              <textarea
                value={userFeedback}
                onChange={(e) => setUserFeedback(e.target.value)}
                placeholder="What were you trying to do when this error occurred?"
                className="w-full p-3 rounded-lg border resize-none transition-colors duration-200"
                style={{
                  backgroundColor: theme === 'dark' ? '#374151' : '#ffffff',
                  borderColor: colors.border,
                  color: colors.text
                }}
                rows={3}
                maxLength={500}
              />
              
              <div className="flex justify-between items-center">
                <span 
                  className="text-xs"
                  style={{ color: colors.textSecondary }}
                >
                  {userFeedback.length}/500 characters
                </span>
                
                <button
                  onClick={handleFeedbackSubmit}
                  disabled={!userFeedback.trim() || isSubmittingFeedback}
                  className="px-4 py-2 rounded-lg text-sm font-medium transition-all duration-200 disabled:opacity-50 disabled:cursor-not-allowed"
                  style={{
                    backgroundColor: colors.primary,
                    color: colors.primaryForeground
                  }}
                >
                  {isSubmittingFeedback ? 'Sending...' : 'Send Feedback'}
                </button>
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default ErrorFallback;
