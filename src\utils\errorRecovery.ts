/**
 * Error Recovery System
 * Provides automated and manual recovery strategies for different error types
 */

import type { AppError } from './errorTypes';
import { RecoveryAction, shouldAutoRetry, getRetryDelay } from './errorTypes';
import { reportError } from './errorReporting';

// Recovery strategy configuration
export interface RecoveryStrategy {
  action: RecoveryAction;
  label: string;
  description: string;
  icon: string;
  primary?: boolean;
  autoExecute?: boolean;
  confirmationRequired?: boolean;
  handler: () => Promise<void> | void;
}

// Recovery context for better decision making
export interface RecoveryContext {
  error: AppError;
  component?: string;
  route?: string;
  retryCount?: number;
  userInitiated?: boolean;
}

/**
 * Error Recovery Service
 * Manages error recovery strategies and execution
 */
class ErrorRecoveryService {
  private retryAttempts: Map<string, number> = new Map();
  private recoveryHistory: Map<string, Date> = new Map();

  /**
   * Gets available recovery strategies for an error
   */
  getRecoveryStrategies(context: RecoveryContext): RecoveryStrategy[] {
    const { error } = context;
    const strategies: RecoveryStrategy[] = [];

    // Get suggested actions from error classification
    const suggestedActions = this.getSuggestedActions(error);

    for (const action of suggestedActions) {
      const strategy = this.createRecoveryStrategy(action, context);
      if (strategy) {
        strategies.push(strategy);
      }
    }

    // Sort strategies by priority (primary first)
    return strategies.sort((a, b) => {
      if (a.primary && !b.primary) return -1;
      if (!a.primary && b.primary) return 1;
      return 0;
    });
  }

  /**
   * Executes automatic recovery if applicable
   */
  async executeAutoRecovery(context: RecoveryContext): Promise<boolean> {
    const { error } = context;
    const errorKey = this.getErrorKey(error);
    const currentRetryCount = this.retryAttempts.get(errorKey) || 0;

    // Check if auto retry is applicable
    if (shouldAutoRetry(error, currentRetryCount)) {
      const delay = getRetryDelay(error, currentRetryCount);
      
      // Update retry count
      this.retryAttempts.set(errorKey, currentRetryCount + 1);
      
      // Wait for retry delay
      await new Promise(resolve => setTimeout(resolve, delay));
      
      // Execute retry strategy
      try {
        await this.executeRetry(context);
        
        // Reset retry count on success
        this.retryAttempts.delete(errorKey);
        return true;
      } catch (retryError) {
        // Log retry failure
        console.warn('Auto retry failed:', retryError);
        return false;
      }
    }

    return false;
  }

  /**
   * Executes a specific recovery strategy
   */
  async executeRecoveryStrategy(
    strategy: RecoveryStrategy,
    context: RecoveryContext
  ): Promise<void> {
    try {
      // Record recovery attempt
      const errorKey = this.getErrorKey(context.error);
      this.recoveryHistory.set(errorKey, new Date());

      // Execute the strategy
      await strategy.handler();

      // Report successful recovery
      await reportError(context.error, {
        recoveryAction: strategy.action,
        recoverySuccess: true,
        component: context.component,
        route: context.route
      });
    } catch (recoveryError) {
      // Report failed recovery
      await reportError(context.error, {
        recoveryAction: strategy.action,
        recoverySuccess: false,
        recoveryError: recoveryError instanceof Error ? recoveryError.message : 'Unknown error',
        component: context.component,
        route: context.route
      });
      
      throw recoveryError;
    }
  }

  /**
   * Creates a recovery strategy for a specific action
   */
  private createRecoveryStrategy(
    action: RecoveryAction,
    context: RecoveryContext
  ): RecoveryStrategy | null {
    switch (action) {
      case RecoveryAction.RETRY:
        return {
          action,
          label: 'Try Again',
          description: 'Retry the failed operation',
          icon: '🔄',
          primary: true,
          handler: () => this.executeRetry(context)
        };

      case RecoveryAction.RELOAD:
        return {
          action,
          label: 'Reload Page',
          description: 'Refresh the current page',
          icon: '🔃',
          confirmationRequired: true,
          handler: () => this.executeReload()
        };

      case RecoveryAction.NAVIGATE_BACK:
        return {
          action,
          label: 'Go Back',
          description: 'Return to the previous page',
          icon: '⬅️',
          handler: () => this.executeNavigateBack()
        };

      case RecoveryAction.RESET_COMPONENT:
        return {
          action,
          label: 'Reset',
          description: 'Reset the current component',
          icon: '🔄',
          handler: () => this.executeResetComponent(context)
        };

      case RecoveryAction.CLEAR_CACHE:
        return {
          action,
          label: 'Clear Cache',
          description: 'Clear application cache and reload',
          icon: '🗑️',
          confirmationRequired: true,
          handler: () => this.executeClearCache()
        };

      case RecoveryAction.LOGOUT:
        return {
          action,
          label: 'Sign Out',
          description: 'Sign out and return to login',
          icon: '🚪',
          confirmationRequired: true,
          handler: () => this.executeLogout()
        };

      case RecoveryAction.CONTACT_SUPPORT:
        return {
          action,
          label: 'Contact Support',
          description: 'Get help from our support team',
          icon: '💬',
          handler: () => this.executeContactSupport(context)
        };

      case RecoveryAction.NONE:
      default:
        return null;
    }
  }

  /**
   * Gets suggested recovery actions for an error
   */
  private getSuggestedActions(error: AppError): RecoveryAction[] {
    // This would typically come from the error classification
    // For now, we'll provide some default suggestions based on error type
    switch (error.type) {
      case 'NETWORK':
        return [RecoveryAction.RETRY, RecoveryAction.RELOAD];
      case 'TIMEOUT':
        return [RecoveryAction.RETRY, RecoveryAction.RELOAD];
      case 'API':
        return [RecoveryAction.RETRY, RecoveryAction.CONTACT_SUPPORT];
      case 'AUTHENTICATION':
        return [RecoveryAction.LOGOUT];
      case 'AUTHORIZATION':
        return [RecoveryAction.NAVIGATE_BACK, RecoveryAction.CONTACT_SUPPORT];
      case 'CHUNK_LOAD':
        return [RecoveryAction.RELOAD, RecoveryAction.CLEAR_CACHE];
      case 'RUNTIME':
        return [RecoveryAction.RESET_COMPONENT, RecoveryAction.RELOAD];
      default:
        return [RecoveryAction.RETRY, RecoveryAction.RELOAD, RecoveryAction.CONTACT_SUPPORT];
    }
  }

  /**
   * Recovery action implementations
   */
  private async executeRetry(context: RecoveryContext): Promise<void> {
    // This would typically trigger a re-render or re-execution
    // The specific implementation depends on the context
    if (context.component) {
      // Trigger component re-render
      window.dispatchEvent(new CustomEvent('error-recovery-retry', {
        detail: { component: context.component, error: context.error }
      }));
    } else {
      // Generic retry - reload the page
      window.location.reload();
    }
  }

  private executeReload(): void {
    window.location.reload();
  }

  private executeNavigateBack(): void {
    if (window.history.length > 1) {
      window.history.back();
    } else {
      window.location.href = '/';
    }
  }

  private executeResetComponent(context: RecoveryContext): void {
    // Dispatch custom event for component reset
    window.dispatchEvent(new CustomEvent('error-recovery-reset', {
      detail: { component: context.component, error: context.error }
    }));
  }

  private async executeClearCache(): Promise<void> {
    // Clear various caches
    if ('caches' in window) {
      const cacheNames = await caches.keys();
      await Promise.all(cacheNames.map(name => caches.delete(name)));
    }

    // Clear localStorage and sessionStorage
    localStorage.clear();
    sessionStorage.clear();

    // Reload the page
    window.location.reload();
  }

  private executeLogout(): void {
    // Clear authentication data
    localStorage.removeItem('auth_token');
    sessionStorage.removeItem('auth_token');
    
    // Redirect to login
    window.location.href = '/login';
  }

  private executeContactSupport(context: RecoveryContext): void {
    // This could open a support modal, redirect to support page, etc.
    const supportUrl = '/support';
    const errorDetails = encodeURIComponent(JSON.stringify({
      type: context.error.type,
      message: context.error.message,
      timestamp: context.error.timestamp,
      component: context.component,
      route: context.route
    }));
    
    window.open(`${supportUrl}?error=${errorDetails}`, '_blank');
  }

  /**
   * Generates a unique key for error tracking
   */
  private getErrorKey(error: AppError): string {
    return `${error.type}-${error.message}`;
  }

  /**
   * Resets recovery tracking
   */
  reset(): void {
    this.retryAttempts.clear();
    this.recoveryHistory.clear();
  }

  /**
   * Gets recovery statistics
   */
  getStats(): {
    retryAttempts: Record<string, number>;
    recoveryHistory: Record<string, Date>;
  } {
    return {
      retryAttempts: Object.fromEntries(this.retryAttempts),
      recoveryHistory: Object.fromEntries(this.recoveryHistory)
    };
  }
}

// Global error recovery service instance
export const errorRecoveryService = new ErrorRecoveryService();

/**
 * Convenience functions for error recovery
 */
export function getRecoveryStrategies(context: RecoveryContext): RecoveryStrategy[] {
  return errorRecoveryService.getRecoveryStrategies(context);
}

export function executeAutoRecovery(context: RecoveryContext): Promise<boolean> {
  return errorRecoveryService.executeAutoRecovery(context);
}

export function executeRecoveryStrategy(
  strategy: RecoveryStrategy,
  context: RecoveryContext
): Promise<void> {
  return errorRecoveryService.executeRecoveryStrategy(strategy, context);
}
