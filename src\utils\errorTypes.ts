/**
 * Error Types and Classification System
 * Provides comprehensive error categorization and metadata for better error handling
 */

// Error type classification
export const ErrorType = {
  // Network and API errors
  NETWORK: 'NETWORK',
  API: 'API',
  TIMEOUT: 'TIMEOUT',

  // Application errors
  RUNTIME: 'RUNTIME',
  BOUNDARY: 'BOUNDARY',
  CHUNK_LOAD: 'CHUNK_LOAD',

  // User and validation errors
  VALIDATION: 'VALIDATION',
  AUTHENTICATION: 'AUTHENTICATION',
  AUTHORIZATION: 'AUTHORIZATION',

  // System errors
  CONFIGURATION: 'CONFIGURATION',
  STORAGE: 'STORAGE',
  UNKNOWN: 'UNKNOWN'
} as const;

export type ErrorType = typeof ErrorType[keyof typeof ErrorType];

// Error severity levels
export const ErrorSeverity = {
  LOW: 'LOW',           // Minor issues, app continues normally
  MEDIUM: 'MEDIUM',     // Noticeable issues, some features affected
  HIGH: 'HIGH',         // Major issues, significant functionality lost
  CRITICAL: 'CRITICAL'  // App-breaking issues, requires immediate attention
} as const;

export type ErrorSeverity = typeof ErrorSeverity[keyof typeof ErrorSeverity];

// Recovery action types
export const RecoveryAction = {
  RETRY: 'RETRY',
  RELOAD: 'RELOAD',
  NAVIGATE_BACK: 'NAVIGATE_BACK',
  RESET_COMPONENT: 'RESET_COMPONENT',
  CLEAR_CACHE: 'CLEAR_CACHE',
  LOGOUT: 'LOGOUT',
  CONTACT_SUPPORT: 'CONTACT_SUPPORT',
  NONE: 'NONE'
} as const;

export type RecoveryAction = typeof RecoveryAction[keyof typeof RecoveryAction];

// Base error interface that extends the standard Error
export interface AppError extends Error {
  code?: string;
  type: ErrorType;
  severity: ErrorSeverity;
  context?: Record<string, any>;
  timestamp: Date;
  userMessage?: string;
  technicalMessage?: string;
  recoverable: boolean;
  retryable: boolean;
  reportable: boolean;
}

// Error context for better debugging
export interface ErrorContext {
  component?: string;
  route?: string;
  userId?: string;
  sessionId?: string;
  userAgent?: string;
  url?: string;
  timestamp: Date;
  buildVersion?: string;
  environment?: string;
  additionalData?: Record<string, any>;
}

// Error classification configuration
export interface ErrorClassification {
  type: ErrorType;
  severity: ErrorSeverity;
  userMessage: string;
  technicalMessage?: string;
  recoverable: boolean;
  retryable: boolean;
  reportable: boolean;
  suggestedActions: RecoveryAction[];
  autoRetryCount?: number;
  autoRetryDelay?: number;
}

// Predefined error classifications
export const ERROR_CLASSIFICATIONS: Record<string, ErrorClassification> = {
  // Network errors
  NETWORK_OFFLINE: {
    type: ErrorType.NETWORK,
    severity: ErrorSeverity.HIGH,
    userMessage: 'You appear to be offline. Please check your internet connection.',
    recoverable: true,
    retryable: true,
    reportable: false,
    suggestedActions: [RecoveryAction.RETRY, RecoveryAction.RELOAD],
    autoRetryCount: 3,
    autoRetryDelay: 2000
  },
  
  NETWORK_TIMEOUT: {
    type: ErrorType.TIMEOUT,
    severity: ErrorSeverity.MEDIUM,
    userMessage: 'The request is taking longer than expected. Please try again.',
    recoverable: true,
    retryable: true,
    reportable: true,
    suggestedActions: [RecoveryAction.RETRY, RecoveryAction.RELOAD],
    autoRetryCount: 2,
    autoRetryDelay: 3000
  },
  
  // API errors
  API_SERVER_ERROR: {
    type: ErrorType.API,
    severity: ErrorSeverity.HIGH,
    userMessage: 'We\'re experiencing technical difficulties. Please try again later.',
    recoverable: true,
    retryable: true,
    reportable: true,
    suggestedActions: [RecoveryAction.RETRY, RecoveryAction.CONTACT_SUPPORT],
    autoRetryCount: 1,
    autoRetryDelay: 5000
  },
  
  API_NOT_FOUND: {
    type: ErrorType.API,
    severity: ErrorSeverity.MEDIUM,
    userMessage: 'The requested resource was not found.',
    recoverable: true,
    retryable: false,
    reportable: true,
    suggestedActions: [RecoveryAction.NAVIGATE_BACK, RecoveryAction.RELOAD]
  },
  
  // Authentication errors
  AUTH_EXPIRED: {
    type: ErrorType.AUTHENTICATION,
    severity: ErrorSeverity.MEDIUM,
    userMessage: 'Your session has expired. Please sign in again.',
    recoverable: true,
    retryable: false,
    reportable: false,
    suggestedActions: [RecoveryAction.LOGOUT]
  },
  
  AUTH_UNAUTHORIZED: {
    type: ErrorType.AUTHORIZATION,
    severity: ErrorSeverity.MEDIUM,
    userMessage: 'You don\'t have permission to access this resource.',
    recoverable: false,
    retryable: false,
    reportable: true,
    suggestedActions: [RecoveryAction.NAVIGATE_BACK, RecoveryAction.CONTACT_SUPPORT]
  },
  
  // Runtime errors
  RUNTIME_ERROR: {
    type: ErrorType.RUNTIME,
    severity: ErrorSeverity.HIGH,
    userMessage: 'Something went wrong. We\'re working to fix this issue.',
    recoverable: true,
    retryable: true,
    reportable: true,
    suggestedActions: [RecoveryAction.RELOAD, RecoveryAction.RESET_COMPONENT]
  },
  
  CHUNK_LOAD_ERROR: {
    type: ErrorType.CHUNK_LOAD,
    severity: ErrorSeverity.MEDIUM,
    userMessage: 'Failed to load application resources. Please refresh the page.',
    recoverable: true,
    retryable: true,
    reportable: true,
    suggestedActions: [RecoveryAction.RELOAD, RecoveryAction.CLEAR_CACHE],
    autoRetryCount: 1,
    autoRetryDelay: 1000
  },
  
  // Validation errors
  VALIDATION_ERROR: {
    type: ErrorType.VALIDATION,
    severity: ErrorSeverity.LOW,
    userMessage: 'Please check your input and try again.',
    recoverable: true,
    retryable: false,
    reportable: false,
    suggestedActions: [RecoveryAction.NONE]
  },
  
  // Unknown errors
  UNKNOWN_ERROR: {
    type: ErrorType.UNKNOWN,
    severity: ErrorSeverity.MEDIUM,
    userMessage: 'An unexpected error occurred. Please try again.',
    recoverable: true,
    retryable: true,
    reportable: true,
    suggestedActions: [RecoveryAction.RETRY, RecoveryAction.RELOAD, RecoveryAction.CONTACT_SUPPORT]
  }
};

/**
 * Creates a standardized AppError from various error sources
 */
export function createAppError(
  error: Error | string | unknown,
  classification?: string | ErrorClassification,
  context?: Partial<ErrorContext>
): AppError {
  const timestamp = new Date();
  
  // Determine base error
  let baseError: Error;
  if (error instanceof Error) {
    baseError = error;
  } else if (typeof error === 'string') {
    baseError = new Error(error);
  } else {
    baseError = new Error('Unknown error occurred');
  }
  
  // Get classification
  let config: ErrorClassification;
  if (typeof classification === 'string') {
    config = ERROR_CLASSIFICATIONS[classification] || ERROR_CLASSIFICATIONS.UNKNOWN_ERROR;
  } else if (classification) {
    config = classification;
  } else {
    config = classifyError(baseError);
  }
  
  // Create enhanced error context
  const errorContext: ErrorContext = {
    timestamp,
    userAgent: typeof navigator !== 'undefined' ? navigator.userAgent : undefined,
    url: typeof window !== 'undefined' ? window.location.href : undefined,
    environment: import.meta.env.MODE,
    buildVersion: import.meta.env.VITE_APP_VERSION || 'unknown',
    ...context
  };
  
  // Create AppError
  const appError: AppError = {
    ...baseError,
    name: baseError.name,
    message: baseError.message,
    stack: baseError.stack,
    type: config.type,
    severity: config.severity,
    context: errorContext,
    timestamp,
    userMessage: config.userMessage,
    technicalMessage: config.technicalMessage || baseError.message,
    recoverable: config.recoverable,
    retryable: config.retryable,
    reportable: config.reportable
  };
  
  return appError;
}

/**
 * Automatically classifies errors based on their properties
 */
export function classifyError(error: Error): ErrorClassification {
  const message = error.message.toLowerCase();
  const name = error.name.toLowerCase();
  
  // Network errors
  if (message.includes('network') || message.includes('fetch') || name.includes('networkerror')) {
    return ERROR_CLASSIFICATIONS.NETWORK_OFFLINE;
  }
  
  if (message.includes('timeout') || name.includes('timeout')) {
    return ERROR_CLASSIFICATIONS.NETWORK_TIMEOUT;
  }
  
  // Chunk loading errors
  if (message.includes('loading chunk') || message.includes('loading css chunk')) {
    return ERROR_CLASSIFICATIONS.CHUNK_LOAD_ERROR;
  }
  
  // Authentication errors
  if (message.includes('unauthorized') || message.includes('401')) {
    return ERROR_CLASSIFICATIONS.AUTH_EXPIRED;
  }
  
  if (message.includes('forbidden') || message.includes('403')) {
    return ERROR_CLASSIFICATIONS.AUTH_UNAUTHORIZED;
  }
  
  // API errors
  if (message.includes('404') || message.includes('not found')) {
    return ERROR_CLASSIFICATIONS.API_NOT_FOUND;
  }
  
  if (message.includes('500') || message.includes('server error')) {
    return ERROR_CLASSIFICATIONS.API_SERVER_ERROR;
  }
  
  // Default to runtime error
  return ERROR_CLASSIFICATIONS.RUNTIME_ERROR;
}

/**
 * Checks if an error should be automatically retried
 */
export function shouldAutoRetry(error: AppError, currentRetryCount: number = 0): boolean {
  if (!error.retryable) return false;

  const classification = ERROR_CLASSIFICATIONS[error.type] || ERROR_CLASSIFICATIONS.UNKNOWN_ERROR;
  const maxRetries = classification.autoRetryCount || 0;

  return currentRetryCount < maxRetries;
}

/**
 * Gets the delay for the next retry attempt
 */
export function getRetryDelay(error: AppError, retryCount: number = 0): number {
  const classification = ERROR_CLASSIFICATIONS[error.type] || ERROR_CLASSIFICATIONS.UNKNOWN_ERROR;
  const baseDelay = classification.autoRetryDelay || 1000;

  // Exponential backoff with jitter
  const exponentialDelay = baseDelay * Math.pow(2, retryCount);
  const jitter = Math.random() * 0.1 * exponentialDelay;

  return Math.min(exponentialDelay + jitter, 30000); // Max 30 seconds
}
