/**
 * Global Error Handler
 * Handles unhandled errors, promise rejections, and other global error events
 */

import { createAppError, ErrorType } from './errorTypes';
import { reportError } from './errorReporting';

/**
 * Global error handler configuration
 */
interface GlobalErrorHandlerConfig {
  enableConsoleLogging: boolean;
  enableReporting: boolean;
  enableUserNotification: boolean;
  maxErrorsPerSession: number;
}

/**
 * Global Error Handler Service
 */
class GlobalErrorHandlerService {
  private config: GlobalErrorHandlerConfig;
  private errorCount: number = 0;
  private isInitialized: boolean = false;

  constructor(config: GlobalErrorHandlerConfig) {
    this.config = config;
  }

  /**
   * Initialize global error handlers
   */
  initialize(): void {
    if (this.isInitialized) {
      console.warn('Global error handler already initialized');
      return;
    }

    // Handle unhandled JavaScript errors
    window.addEventListener('error', this.handleError.bind(this));

    // Handle unhandled promise rejections
    window.addEventListener('unhandledrejection', this.handlePromiseRejection.bind(this));

    // Handle resource loading errors (images, scripts, etc.)
    window.addEventListener('error', this.handleResourceError.bind(this), true);

    this.isInitialized = true;
    console.log('Global error handler initialized');
  }

  /**
   * Cleanup global error handlers
   */
  cleanup(): void {
    if (!this.isInitialized) {
      return;
    }

    window.removeEventListener('error', this.handleError.bind(this));
    window.removeEventListener('unhandledrejection', this.handlePromiseRejection.bind(this));
    window.removeEventListener('error', this.handleResourceError.bind(this), true);

    this.isInitialized = false;
    console.log('Global error handler cleaned up');
  }

  /**
   * Handle JavaScript errors
   */
  private async handleError(event: ErrorEvent): Promise<void> {
    if (!this.shouldHandleError()) {
      return;
    }

    const error = createAppError(
      event.error || new Error(event.message),
      'RUNTIME_ERROR',
      {
        component: 'GlobalErrorHandler',
        route: window.location.pathname,
        additionalData: {
          filename: event.filename,
          lineno: event.lineno,
          colno: event.colno,
          source: 'window.onerror'
        }
      }
    );

    await this.processError(error, 'JavaScript Error');
  }

  /**
   * Handle unhandled promise rejections
   */
  private async handlePromiseRejection(event: PromiseRejectionEvent): Promise<void> {
    if (!this.shouldHandleError()) {
      return;
    }

    // Prevent the default browser behavior (console error)
    event.preventDefault();

    const error = createAppError(
      event.reason instanceof Error ? event.reason : new Error(String(event.reason)),
      'RUNTIME_ERROR',
      {
        component: 'GlobalErrorHandler',
        route: window.location.pathname,
        additionalData: {
          source: 'unhandledrejection',
          reason: event.reason
        }
      }
    );

    await this.processError(error, 'Unhandled Promise Rejection');
  }

  /**
   * Handle resource loading errors
   */
  private async handleResourceError(event: Event): Promise<void> {
    const target = event.target as HTMLElement;
    
    // Only handle resource loading errors, not JavaScript errors
    if (!target || target === window) {
      return;
    }

    if (!this.shouldHandleError()) {
      return;
    }

    const tagName = target.tagName?.toLowerCase();
    const src = (target as any).src || (target as any).href;

    const error = createAppError(
      new Error(`Failed to load ${tagName}: ${src}`),
      'CHUNK_LOAD_ERROR',
      {
        component: 'GlobalErrorHandler',
        route: window.location.pathname,
        additionalData: {
          tagName,
          src,
          source: 'resource-error'
        }
      }
    );

    await this.processError(error, 'Resource Loading Error');
  }

  /**
   * Process and report errors
   */
  private async processError(error: any, errorType: string): Promise<void> {
    this.errorCount++;

    if (this.config.enableConsoleLogging) {
      console.group(`🚨 Global ${errorType}`);
      console.error('Error:', error);
      console.error('Context:', error.context);
      console.groupEnd();
    }

    if (this.config.enableReporting) {
      try {
        await reportError(error, {
          globalHandler: true,
          errorType,
          sessionErrorCount: this.errorCount
        });
      } catch (reportingError) {
        console.error('Failed to report global error:', reportingError);
      }
    }

    if (this.config.enableUserNotification && this.shouldShowUserNotification(error)) {
      this.showUserNotification(error, errorType);
    }
  }

  /**
   * Check if error should be handled
   */
  private shouldHandleError(): boolean {
    return this.errorCount < this.config.maxErrorsPerSession;
  }

  /**
   * Check if user should be notified about this error
   */
  private shouldShowUserNotification(error: any): boolean {
    // Don't show notifications for low severity errors
    if (error.severity === 'LOW') {
      return false;
    }

    // Don't show too many notifications
    if (this.errorCount > 3) {
      return false;
    }

    return true;
  }

  /**
   * Show user notification for critical errors
   */
  private showUserNotification(error: any, errorType: string): void {
    // Create a simple toast notification
    const notification = document.createElement('div');
    notification.className = `
      fixed top-4 right-4 z-50 max-w-sm p-4 rounded-lg shadow-lg
      bg-red-50 border border-red-200 text-red-800
      dark:bg-red-900/20 dark:border-red-800 dark:text-red-200
    `;
    
    notification.innerHTML = `
      <div class="flex items-start">
        <div class="flex-shrink-0">
          <span class="text-red-500">⚠️</span>
        </div>
        <div class="ml-3 flex-1">
          <h4 class="text-sm font-medium">Something went wrong</h4>
          <p class="mt-1 text-sm">${error.userMessage || 'An unexpected error occurred'}</p>
          <button 
            onclick="this.parentElement.parentElement.parentElement.remove()"
            class="mt-2 text-xs underline hover:no-underline"
          >
            Dismiss
          </button>
        </div>
      </div>
    `;

    document.body.appendChild(notification);

    // Auto-remove after 10 seconds
    setTimeout(() => {
      if (notification.parentNode) {
        notification.parentNode.removeChild(notification);
      }
    }, 10000);
  }

  /**
   * Update configuration
   */
  updateConfig(newConfig: Partial<GlobalErrorHandlerConfig>): void {
    this.config = { ...this.config, ...newConfig };
  }

  /**
   * Get error statistics
   */
  getStats(): { errorCount: number; isInitialized: boolean } {
    return {
      errorCount: this.errorCount,
      isInitialized: this.isInitialized
    };
  }

  /**
   * Reset error count
   */
  reset(): void {
    this.errorCount = 0;
  }
}

// Default configuration
const defaultConfig: GlobalErrorHandlerConfig = {
  enableConsoleLogging: true,
  enableReporting: true,
  enableUserNotification: true,
  maxErrorsPerSession: 20
};

// Global instance
export const globalErrorHandler = new GlobalErrorHandlerService(defaultConfig);

/**
 * Initialize global error handling
 */
export function initializeGlobalErrorHandler(config?: Partial<GlobalErrorHandlerConfig>): void {
  if (config) {
    globalErrorHandler.updateConfig(config);
  }
  globalErrorHandler.initialize();
}

/**
 * Cleanup global error handling
 */
export function cleanupGlobalErrorHandler(): void {
  globalErrorHandler.cleanup();
}

/**
 * Get global error handler statistics
 */
export function getGlobalErrorStats(): { errorCount: number; isInitialized: boolean } {
  return globalErrorHandler.getStats();
}
